using Keleid.BLL.Services.Interfaces;
using Keleid.DAL;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Xml;

namespace Keleid.BLL.Services
{
    public class SitemapService : ISitemapService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SitemapService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _sitemapCachePath;
        private static readonly object _lockObject = new object();

        public SitemapService(ApplicationDbContext context, ILogger<SitemapService> logger, IConfiguration configuration)
        {
            _context = context;
            _logger = logger;
            _configuration = configuration;
            _sitemapCachePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "sitemap.xml");
        }

        public async Task<string> GenerateSitemapXmlAsync()
        {
            try
            {
                var sitemap = new StringBuilder();
                sitemap.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                sitemap.AppendLine("<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">");

                // صفحات ثابت
                await AddStaticPagesAsync(sitemap);

                // صفحات دسته‌بندی‌ها
                await AddCategoryPagesAsync(sitemap);

                // صفحات آگهی‌های تایید شده
                await AddAdvertisementPagesAsync(sitemap);

                sitemap.AppendLine("</urlset>");

                var xmlContent = sitemap.ToString();
                
                // ذخیره در فایل cache
                await SaveSitemapToCacheAsync(xmlContent);

                _logger.LogInformation("Sitemap generated successfully with {Count} URLs", CountUrls(xmlContent));
                return xmlContent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating sitemap XML");
                throw;
            }
        }

        private async Task AddStaticPagesAsync(StringBuilder sitemap)
        {
            var baseUrl = _configuration["SiteSettings:BaseUrl"] ?? "https://keleid.ir";
            var staticPages = new[]
            {
                new { Url = "/", Priority = "1.0", ChangeFreq = "daily" },
                new { Url = "/Info/About", Priority = "0.8", ChangeFreq = "monthly" },
                new { Url = "/Info/Terms", Priority = "0.6", ChangeFreq = "monthly" },
                new { Url = "/Info/Contact", Priority = "0.7", ChangeFreq = "monthly" },
                new { Url = "/Info/Download", Priority = "0.7", ChangeFreq = "weekly" },
                new { Url = "/Account/Index", Priority = "0.5", ChangeFreq = "monthly" }
            };

            foreach (var page in staticPages)
            {
                sitemap.AppendLine("  <url>");
                sitemap.AppendLine($"    <loc>{baseUrl}{page.Url}</loc>");
                sitemap.AppendLine($"    <lastmod>{DateTime.UtcNow:yyyy-MM-dd}</lastmod>");
                sitemap.AppendLine($"    <changefreq>{page.ChangeFreq}</changefreq>");
                sitemap.AppendLine($"    <priority>{page.Priority}</priority>");
                sitemap.AppendLine("  </url>");
            }
        }

        private async Task AddCategoryPagesAsync(StringBuilder sitemap)
        {
            var baseUrl = _configuration["SiteSettings:BaseUrl"] ?? "https://keleid.ir";
            
            var categories = await _context.Categories
                .Where(c => !string.IsNullOrEmpty(c.Slug))
                .Select(c => new { c.Slug, c.ParentCategoryId })
                .ToListAsync();

            foreach (var category in categories)
            {
                sitemap.AppendLine("  <url>");
                sitemap.AppendLine($"    <loc>{baseUrl}/{category.Slug}</loc>");
                sitemap.AppendLine($"    <lastmod>{DateTime.UtcNow:yyyy-MM-dd}</lastmod>");
                sitemap.AppendLine("    <changefreq>weekly</changefreq>");
                sitemap.AppendLine($"    <priority>{(category.ParentCategoryId == null ? "0.9" : "0.8")}</priority>");
                sitemap.AppendLine("  </url>");
            }
        }

        private async Task AddAdvertisementPagesAsync(StringBuilder sitemap)
        {
            var baseUrl = _configuration["SiteSettings:BaseUrl"] ?? "https://keleid.ir";
            
            var advertisements = await _context.Advertisements
                .Where(a => a.IsApproved && !a.IsDeleted && !string.IsNullOrEmpty(a.Slug))
                .Select(a => new { a.Id, a.Slug, a.CreatedAt })
                .ToListAsync();

            foreach (var ad in advertisements)
            {
                sitemap.AppendLine("  <url>");
                sitemap.AppendLine($"    <loc>{baseUrl}/{ad.Id}/{ad.Slug}</loc>");
                sitemap.AppendLine($"    <lastmod>{ad.CreatedAt:yyyy-MM-dd}</lastmod>");
                sitemap.AppendLine("    <changefreq>monthly</changefreq>");
                sitemap.AppendLine("    <priority>0.7</priority>");
                sitemap.AppendLine("  </url>");
            }
        }

        public async Task<bool> RefreshSitemapAsync()
        {
            try
            {
                lock (_lockObject)
                {
                    // بررسی اینکه آیا فایل sitemap در حال حاضر در حال به‌روزرسانی است یا نه
                    if (File.Exists(_sitemapCachePath + ".lock"))
                    {
                        return false; // در حال به‌روزرسانی است
                    }

                    // ایجاد فایل قفل
                    File.WriteAllText(_sitemapCachePath + ".lock", DateTime.UtcNow.ToString());
                }

                await GenerateSitemapXmlAsync();

                // حذف فایل قفل
                if (File.Exists(_sitemapCachePath + ".lock"))
                {
                    File.Delete(_sitemapCachePath + ".lock");
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing sitemap");
                
                // حذف فایل قفل در صورت خطا
                if (File.Exists(_sitemapCachePath + ".lock"))
                {
                    File.Delete(_sitemapCachePath + ".lock");
                }
                
                return false;
            }
        }

        public async Task<DateTime?> GetLastModifiedAsync()
        {
            try
            {
                if (File.Exists(_sitemapCachePath))
                {
                    return File.GetLastWriteTimeUtc(_sitemapCachePath);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sitemap last modified date");
                return null;
            }
        }

        public async Task<bool> NeedsUpdateAsync()
        {
            try
            {
                var lastModified = await GetLastModifiedAsync();
                if (lastModified == null)
                {
                    return true; // فایل وجود ندارد، نیاز به ایجاد دارد
                }

                // بررسی آخرین تغییر در آگهی‌های تایید شده
                var lastAdChange = await _context.Advertisements
                    .Where(a => a.IsApproved && !a.IsDeleted)
                    .MaxAsync(a => (DateTime?)a.CreatedAt);

                // بررسی آخرین تغییر در دسته‌بندی‌ها (فرض می‌کنیم فیلد UpdatedAt وجود دارد یا از CreatedAt استفاده می‌کنیم)
                var lastCategoryChange = await _context.Categories
                    .MaxAsync(c => (DateTime?)EF.Property<DateTime>(c, "CreatedAt"));

                var lastDataChange = new[] { lastAdChange, lastCategoryChange }
                    .Where(d => d.HasValue)
                    .Max();

                return lastDataChange > lastModified;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if sitemap needs update");
                return true; // در صورت خطا، به‌روزرسانی کن
            }
        }

        private async Task SaveSitemapToCacheAsync(string xmlContent)
        {
            try
            {
                var directory = Path.GetDirectoryName(_sitemapCachePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                await File.WriteAllTextAsync(_sitemapCachePath, xmlContent, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving sitemap to cache");
                throw;
            }
        }

        private int CountUrls(string xmlContent)
        {
            return xmlContent.Split("<url>").Length - 1;
        }
    }
}
