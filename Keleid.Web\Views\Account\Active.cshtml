﻿@await Html.PartialAsync("_Account")

<div class="login-container">
    <div class="login-box">
        <div class="login-header">
            <div class="logo">
                <a asp-controller="Home" asp-action="Index"><img src="/assets/img/logo.png" alt="لوگو" class="logo-img"></a>
            </div>
            <a asp-action="Index" class="back-btn">
                <i class="fa fa-angle-right"></i>
            </a>
        </div>

        <div class="auth-section">
            <h4>کد تایید را وارد کنید</h4>
            <p class="verification-desc">کد تایید به شماره <span id="verificationPhone">0935</span> ارسال شد</p>

            <form id="verificationForm" class="login-form">
                @Html.AntiForgeryToken()
                <div class="mb-3">
                    <div class="verification-input">
                        <input type="text" id="verificationCode" name="verificationCode" placeholder="- - - - -"
                               maxlength="5" required>
                    </div>
                    <div class="error-message" id="verificationError"></div>
                </div>

                <div class="timer-section">
                    <button type="button" class="btn-link mt-0" id="resendCode" disabled>
                        ارسال مجدد کد
                    </button>
                </div>

                <button type="submit" class="btn btn-danger w-100" id="verifyBtn">
                    <span id="verifyBtnText">تایید</span>
                    <i class="bi bi-arrow-left"></i>
                </button>
            </form>
        </div>
    </div>
</div>

@section js {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const verificationForm = document.getElementById('verificationForm');
            const verificationCodeInput = document.getElementById('verificationCode');
            const verificationError = document.getElementById('verificationError');
            const verifyBtn = document.getElementById('verifyBtn');
            const verifyBtnText = document.getElementById('verifyBtnText');
            const resendCodeBtn = document.getElementById('resendCode');
            const timerElement = document.getElementById('timer');
            const verificationPhoneElement = document.getElementById('verificationPhone');

            // دریافت شماره تلفن از server-side
            const phoneNumber = '@@context.Session.GetString("PhoneNumber")';
            if (phoneNumber) {
                // نمایش 4 رقم آخر شماره تلفن
                const maskedPhone = phoneNumber.substring(0, 4) + '***' + phoneNumber.substring(7);
                verificationPhoneElement.textContent = maskedPhone;

                // بررسی وضعیت cooldown هنگام بارگذاری صفحه
                checkCooldownStatus(phoneNumber);
            }

            // تابع بررسی وضعیت cooldown
            function checkCooldownStatus(phoneNumber) {
                fetch(`@Url.Action("CheckCooldown", "Account")?phoneNumber=${encodeURIComponent(phoneNumber)}`)
                .then(response => response.json())
                .then(data => {
                    if (!data.canSend && data.remainingSeconds > 0) {
                        // اگر در حالت cooldown است
                        resendCodeBtn.disabled = true;

                        let remainingTime = data.remainingSeconds;
                        const cooldownInterval = setInterval(() => {
                            if (remainingTime <= 0) {
                                clearInterval(cooldownInterval);
                                resendCodeBtn.disabled = false;
                                resendCodeBtn.textContent = 'ارسال مجدد کد';
                            } else {
                                const minutes = Math.floor(remainingTime / 60);
                                const seconds = remainingTime % 60;
                                resendCodeBtn.textContent = `صبر کنید ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                                remainingTime--;
                            }
                        }, 1000);
                    }
                })
                .catch(error => {
                    console.error('Error checking cooldown:', error);
                });
            }

            // فرمت کردن ورودی کد تایید
            verificationCodeInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, ''); // فقط اعداد
                if (value.length > 5) value = value.substring(0, 5);
                e.target.value = value;
            });

            // ارسال فرم تایید
            verificationForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Clear previous errors
                verificationError.textContent = '';
                verificationError.classList.remove('show');

                const code = verificationCodeInput.value.trim();

                // اعتبارسنجی
                if (!code) {
                    verificationError.textContent = 'لطفا کد تایید را وارد کنید';
                    verificationError.classList.add('show');
                    return;
                }

                if (code.length !== 5) {
                    verificationError.textContent = 'کد تایید باید 5 رقم باشد';
                    verificationError.classList.add('show');
                    return;
                }

                if (!phoneNumber) {
                    verificationError.textContent = 'شماره تلفن یافت نشد';
                    verificationError.classList.add('show');
                    return;
                }

                // Disable button and show loading
                verifyBtn.disabled = true;
                verifyBtnText.textContent = 'در حال تایید...';

                // ارسال درخواست AJAX
                fetch('@Url.Action("VerifyCode", "Account")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    },
                    body: `phoneNumber=${encodeURIComponent(phoneNumber)}&verificationCode=${encodeURIComponent(code)}`
                })
                .then(response => response.json())
                .then(data => {
                    console.log('VerifyCode Response:', data); // Debug log

                    if (data.success) {
                        // تایید موفق، هدایت به صفحه مناسب
                        console.log('Redirecting to:', data.redirectUrl); // Debug log
                        window.location.href = data.redirectUrl;
                    } else {
                        verificationError.textContent = data.message || 'خطایی رخ داده است';
                        verificationError.classList.add('show');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    verificationError.textContent = 'خطای ارتباط با سرور';
                    verificationError.classList.add('show');
                })
                .finally(() => {
                    // Re-enable button
                    verifyBtn.disabled = false;
                    verifyBtnText.textContent = 'تایید';
                });
            });

            // ارسال مجدد کد
            resendCodeBtn.addEventListener('click', function() {
                if (!phoneNumber) {
                    verificationError.textContent = 'شماره تلفن یافت نشد';
                    verificationError.classList.add('show');
                    return;
                }

                resendCodeBtn.disabled = true;
                resendCodeBtn.textContent = 'در حال ارسال...';

                fetch('@Url.Action("ResendCode", "Account")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    },
                    body: `phoneNumber=${encodeURIComponent(phoneNumber)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // ریست تایمر
                        timeLeft = 120;
                        resendCodeBtn.disabled = true;
                        resendCodeBtn.textContent = 'ارسال مجدد کد';

                        // نمایش پیام موفقیت
                        verificationError.textContent = 'کد تایید مجدداً ارسال شد';
                        verificationError.style.color = '#28a745';
                        verificationError.classList.add('show');

                        setTimeout(() => {
                            verificationError.classList.remove('show');
                            verificationError.style.color = '#dc3545';
                        }, 3000);
                    } else {
                        verificationError.textContent = data.message || 'خطا در ارسال کد';
                        verificationError.classList.add('show');

                        // اگر زمان باقی‌مانده مشخص شده، دکمه را غیرفعال نگه داریم
                        if (data.remainingSeconds && data.remainingSeconds > 0) {
                            resendCodeBtn.disabled = true;

                            // تایمر جدید برای زمان باقی‌مانده
                            let remainingTime = data.remainingSeconds;
                            const cooldownInterval = setInterval(() => {
                                if (remainingTime <= 0) {
                                    clearInterval(cooldownInterval);
                                    resendCodeBtn.disabled = false;
                                    resendCodeBtn.textContent = 'ارسال مجدد کد';
                                } else {
                                    const minutes = Math.floor(remainingTime / 60);
                                    const seconds = remainingTime % 60;
                                    resendCodeBtn.textContent = `صبر کنید ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                                    remainingTime--;
                                }
                            }, 1000);
                        } else {
                            resendCodeBtn.disabled = false;
                            resendCodeBtn.textContent = 'ارسال مجدد کد';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    verificationError.textContent = 'خطای ارتباط با سرور';
                    verificationError.classList.add('show');
                    resendCodeBtn.disabled = false;
                    resendCodeBtn.textContent = 'ارسال مجدد کد';
                });
            });
        });
    </script>
}